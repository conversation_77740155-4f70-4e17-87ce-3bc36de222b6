version: '3.8'

services:
  web:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: burnrate-forcaster
    restart: unless-stopped
    image: atemndobs/burnrate-forcaster-amd64:v0.1
    ports:
      - "5522:80"
    env_file:
      - .env  # Load environment variables from .env file
    environment:
      - NODE_ENV=production
      # Add other environment variables here or use env_file above
    volumes:
      - ./.env:/usr/share/nginx/html/.env:ro  # Mount .env file into container
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - app-network

networks:
  app-network:
    driver: bridge