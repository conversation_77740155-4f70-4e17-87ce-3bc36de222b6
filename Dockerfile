# Build stage
FROM node:18 as build

WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./


# Install dependencies
RUN npm install

# Copy source code and .env file
COPY . .

# Build the app with environment variables
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets from build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy .env file for runtime environment variables
COPY --from=build /app/.env* /usr/share/nginx/html/

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]