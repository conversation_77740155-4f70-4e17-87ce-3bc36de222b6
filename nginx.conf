server {
    listen 80;
    server_name localhost;
    
    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # PWA requirements
    add_header Service-Worker-Allowed /;
    
    # MIME types
    types {
        application/manifest+json  webmanifest;
        application/javascript     js;
        text/css                  css;
        image/png                 png;
        image/svg+xml             svg svgz;
        application/x-font-ttf     ttf;
        font/opentype             otf;
        application/vnd.ms-fontobject eot;
        application/font-woff     woff;
        application/font-woff2    woff2;
    }
    
    # Root location
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Enable CORS for PWA
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, no-transform, immutable";
            add_header Access-Control-Allow-Origin *;
        }
        
        # Service Worker and Manifest
        location ~* \.(webmanifest|json)$ {
            expires -1;
            add_header Cache-Control "public, max-age=0, must-revalidate";
            add_header Content-Type application/manifest+json;
            add_header Access-Control-Allow-Origin *;
        }
        
        # Don't cache HTML and service worker
        location ~* \.(html|js)$ {
            expires -1;
            add_header Cache-Control "no-store, no-cache, must-revalidate";
        }
        
        # Block access to sensitive files
        location ~ /\.(?!well-known) {
            deny all;
        }
    }
    
    # Service Worker scope
    location = /sw.js {
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Content-Type application/javascript;
        add_header Access-Control-Allow-Origin *;
        root /usr/share/nginx/html;
    }
    
    # Workbox service worker
    location ~* ^/workbox-.*\.js$ {
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Content-Type application/javascript;
        add_header Access-Control-Allow-Origin *;
        root /usr/share/nginx/html;
    }
    
    # Allow access to .env file
    location ~* ^/\.env$ {
        root /usr/share/nginx/html;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
        add_header Access-Control-Allow-Origin *;
    }

    # Error pages
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    
    # Handle preflight requests
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain; charset=utf-8';
        add_header 'Content-Length' 0;
        return 204;
    }
}