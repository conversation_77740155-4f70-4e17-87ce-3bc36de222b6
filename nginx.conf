server {
    listen 80;
    server_name localhost;

    # Security headers
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # PWA requirements
    add_header Service-Worker-Allowed / always;

    # Root location
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;

        # Enable CORS for PWA
        add_header Access-Control-Allow-Origin '*' always;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS' always;
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, no-transform, immutable" always;
        add_header Access-Control-Allow-Origin '*' always;
    }

    # Service Worker and Manifest files - no cache
    location ~* \.(webmanifest|json)$ {
        root /usr/share/nginx/html;
        expires -1;
        add_header Cache-Control "public, max-age=0, must-revalidate" always;
        add_header Content-Type application/manifest+json always;
        add_header Access-Control-Allow-Origin '*' always;
    }

    # Don't cache HTML files
    location ~* \.html$ {
        root /usr/share/nginx/html;
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate" always;
        add_header Access-Control-Allow-Origin '*' always;
    }

    # Block access to sensitive files
    location ~ /\.(?!well-known) {
        deny all;
    }
    
    # Service Worker files - never cache
    location ~* ^/(sw\.js|registerSW\.js)$ {
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Content-Type application/javascript;
        add_header Access-Control-Allow-Origin *;
        add_header Service-Worker-Allowed /;
        root /usr/share/nginx/html;
    }
    
    # Workbox service worker
    location ~* ^/workbox-.*\.js$ {
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Content-Type application/javascript;
        add_header Access-Control-Allow-Origin *;
        root /usr/share/nginx/html;
    }
    
    # Allow access to .env file
    location ~* ^/\.env$ {
        root /usr/share/nginx/html;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
        add_header Access-Control-Allow-Origin *;
    }

    # Error pages
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}